{"name": "api-upload", "version": "1.0.0", "private": true, "type": "module", "main": "src/index.ts", "scripts": {"predev": "echo 'Skipping DB migration for now'", "dev": "wrangler dev --remote", "dev:local": "npm run db:migrate:local && wrangler dev", "dev:remote": "wrangler dev --remote", "deploy": "wrangler deploy", "postdeploy": "npm-run-all --parallel postdeploy:*", "postdeploy:vars": "echo $R2_ACCESS_KEY_ID | wrangler secret put R2_ACCESS_KEY_ID && echo $R2_SECRET_ACCESS_KEY | wrangler secret put R2_SECRET_ACCESS_KEY && echo $CLOUDFLARE_ACCOUNT_ID | wrangler secret put CLOUDFLARE_ACCOUNT_ID", "postdeploy:db": "wrangler d1 migrations apply DB --remote", "test": "vitest", "test:watch": "vitest --watch", "type-check": "tsc --noEmit", "db:migrate": "wrangler d1 execute DB --file=./migrations/0001_create_uploads_table.sql", "db:migrate:local": "wrangler d1 execute DB --local --file=./migrations/0001_create_uploads_table.sql", "db:migrate:remote": "wrangler d1 execute DB --remote --file=./migrations/0001_create_uploads_table.sql"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.61", "@cloudflare/workers-types": "^4.********.0", "npm-run-all": "^4.1.5", "typescript": "^5.9.2", "vitest": "^3.2.4", "wrangler": "^4.30.0"}, "dependencies": {"hono": "^4.9.0", "pandoc-ts": "^1.0.5"}}