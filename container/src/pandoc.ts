import { Hono } from 'hono';
import { join, dirname, basename, extname } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function convert(filePath: string) {
	const filename = basename(filePath, extname(filePath));
	const fileDir = dirname(filePath);
	const extractMediaPath = join(fileDir, 'attachments', filename);

	const { stderr } = await execAsync(`pandoc t markdown_strict --extract-media='${extractMediaPath}' '${filePath}'`);
	if (stderr) {
		console.warn('Pandoc warning:', stderr);
	}
}
