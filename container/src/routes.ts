import { Hono } from 'hono';
import { join } from 'path';
import { convert } from './pandoc';

const R2_MOUNT_PATH = '/mnt/r2';

export const app = new Hono();

app.post('/compressions', async (c) => {
	const { r2Path } = await c.req.json<{ r2Path: string }>()
	await convert(join(R2_MOUNT_PATH, r2Path));
	return c.text('OK');
});

app.get('/health', (c) => {
	return c.json({ status: 'ok', timestamp: new Date().toISOString() });
});
